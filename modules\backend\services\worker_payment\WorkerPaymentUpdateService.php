<?php

namespace app\modules\backend\services\worker_payment;

use app\common\models\Cashbox;
use app\common\models\CashboxDetail;
use app\common\models\PaymentType;
use app\modules\backend\models\WorkerFinances;
use app\modules\backend\models\WorkerSalary;
use app\modules\backend\models\Worker;
use app\modules\backend\models\WorkerDebt;
use app\modules\backend\models\Expenses;
use app\common\models\Tracking;
use Yii;
use yii\db\Exception;

class WorkerPaymentUpdateService
{
    /**
     * Получить данные для формы редактирования выплаты
     */
    public function getEditFormData(int $workerId, string $month): array
    {
        try {
            // Получаем основные данные формы через основной сервис
            $mainService = new WorkerPaymentService();
            $formData = $mainService->getFormData();
            
            // Получаем данные работника
            $worker = Worker::findOne($workerId);
            if (!$worker) {
                return [
                    'status' => 'error',
                    'message' => 'Работник не найден'
                ];
            }
            
            // Получаем все выплаты работника за указанный месяц
            $existingPayments = WorkerFinances::find()
                ->where(['worker_id' => $workerId, 'month' => $month])
                ->andWhere(['deleted_at' => null])
                ->all();
            
            // Группируем выплаты по типам
            $paymentsByType = [];
            $paymentAmountsByType = [];
            $paymentMethodsByType = [];
            foreach ($existingPayments as $payment) {
                // Определяем базовый тип выплаты и метод оплаты
                $baseType = $payment->type;
                $methodId = PaymentType::CASH; // По умолчанию наличные

                // Для зарплаты используем старую логику
                if ($payment->type == WorkerFinances::TYPE_CASH_SALARY) {
                    $baseType = WorkerFinances::TYPE_SALARY;
                    $methodId = PaymentType::CASH;
                } elseif ($payment->type == WorkerFinances::TYPE_SALARY) {
                    $baseType = WorkerFinances::TYPE_SALARY;
                    $methodId = PaymentType::PAYMENT_CARD;
                } else {
                    // Для всех остальных типов платежей определяем способ оплаты через таблицу expenses
                    $expense = Expenses::find()
                        ->where(['worker_finance_id' => $payment->id])
                        ->andWhere(['deleted_at' => null])
                        ->one();

                    if ($expense && $expense->payment_type) {
                        $methodId = $expense->payment_type;
                    }
                }

                if (!isset($paymentsByType[$baseType])) {
                    $paymentsByType[$baseType] = [];
                    $paymentAmountsByType[$baseType] = 0;
                    $paymentMethodsByType[$baseType] = [];
                }

                // Сохраняем платеж
                $paymentsByType[$baseType][] = $payment;

                // Сумма по базовому типу
                $paymentAmountsByType[$baseType] += $payment->amount;

                // Сумма по методу оплаты
                if (!isset($paymentMethodsByType[$baseType][$methodId])) {
                    $paymentMethodsByType[$baseType][$methodId] = 0;
                }
                $paymentMethodsByType[$baseType][$methodId] += $payment->amount;
            }
            
            // Получаем информацию о зарплате работника
            $workerSalary = WorkerSalary::find()
                ->where(['worker_id' => $workerId])
                ->andWhere(['end_date' => '9999-12-31'])
                ->one();
            
            // Получаем информацию о долге (учитываем уже произведённые погашения)
            $debtRaw = WorkerDebt::find()
                ->where(['worker_id' => $workerId])
                ->andWhere(['>', 'amount', 0])
                ->andWhere(['deleted_at' => null])
                ->sum('amount') ?? 0;

            $debtPayments = WorkerFinances::find()
                ->where(['worker_id' => $workerId, 'type' => WorkerFinances::TYPE_DEBT_PAYMENT])
                ->andWhere(['deleted_at' => null])
                ->sum('amount') ?? 0;

            $debt = max($debtRaw - $debtPayments, 0);
            
            $formData['selectedWorker'] = $worker;
            $formData['selectedMonth'] = $month;
            $formData['existingPayments'] = $paymentsByType;
            $formData['paymentAmountsByType'] = $paymentAmountsByType;
            $formData['paymentMethodsByType'] = $paymentMethodsByType;
            $formData['workerSalary'] = $workerSalary;
            $formData['workerDebt'] = $debt;
            $formData['isEdit'] = true;
            $formData['status'] = 'success';
            
            return $formData;
            
        } catch (\Exception $e) {
            return [
                'status' => 'error',
                'message' => 'Ошибка при загрузке данных: ' . $e->getMessage()
            ];
        }
    }
    
    /**
     * Обновить выплату работнику
     */
    public function updatePayment(array $data): array
    {
        $transaction = Yii::$app->db->beginTransaction();
        
        try {
            $workerId = $data['worker_id'];
            $month = $data['month'];
            $paymentTypes = $data['payment_types'] ?? [];

            // Валидация данных через основной сервис
            $mainService = new WorkerPaymentService();
            // При обновлении игнорируем существующие выплаты, так как они будут перезаписаны
            $data['ignore_existing_payments'] = true;
            $validationResult = $this->validateUpdateData($data, $mainService);
            if ($validationResult['status'] === 'error') {
                return $validationResult;
            }

            // Получаем все существующие выплаты за этот месяц
            $existingPayments = WorkerFinances::find()
                ->where(['worker_id' => $workerId, 'month' => $month])
                ->andWhere(['deleted_at' => null])
                ->all();

            // Сохраняем информацию о старых выплатах для отката касс
            $cashboxRevertData = [];
            
            // Помечаем все существующие выплаты как удаленные и возвращаем деньги в кассы
            foreach ($existingPayments as $payment) {
                $payment->deleted_at = date('Y-m-d H:i:s');
                $payment->save();
                
                // Также помечаем связанные расходы и записи в кассах как удаленные и возвращаем деньги

                // 1. Помечаем связанные расходы как удалённые
                $expenses = Expenses::find()
                    ->where(['worker_finance_id' => $payment->id])
                    ->andWhere(['deleted_at' => null])
                    ->all();

                foreach ($expenses as $expense) {
                    $expense->deleted_at = date('Y-m-d H:i:s');
                    $expense->save();
                }

                // Удаляем связанную запись задолженности, если это выплата типа "Долг"
                if ($payment->type == WorkerFinances::TYPE_DEBT) {
                    $debts = WorkerDebt::find()
                        ->where(['worker_id' => $payment->worker_id])
                        ->andWhere(['deleted_at' => null])
                        ->andWhere(['status' => [null, false]])
                        ->andWhere(['amount' => $payment->amount])
                        ->orderBy(['created_at' => SORT_DESC])
                        ->all();

                    foreach ($debts as $debt) {
                        $debt->deleted_at = date('Y-m-d H:i:s');
                        $debt->save();
                    }
                }

                // 2. Помечаем связанные записи в кассах как удаленные и возвращаем деньги
                $cashboxDetails = CashboxDetail::find()
                    ->where(['worker_finance_id' => $payment->id])
                    ->andWhere(['deleted_at' => null])
                    ->all();
                
                foreach ($cashboxDetails as $detail) {
                    // Возвращаем деньги в кассу
                    $cashbox = Cashbox::findOne($detail->cashbox_id);
                    if ($cashbox) {
                        $cashbox->balance += $detail->amount;
                        $cashbox->save();
                        
                        $cashboxRevertData[] = [
                            'cashbox_id' => $cashbox->id,
                            'amount' => $detail->amount
                        ];
                    }
                    
                    $detail->deleted_at = date('Y-m-d H:i:s');
                    $detail->save();
                }
            }

            // Создаем новые выплаты используя основной сервис
            $result = $mainService->createPayment($data);
            
            if ($result['status'] === 'success') {
                $transaction->commit();
                return [
                    'status' => 'success',
                    'message' => 'Выплата успешно обновлена',
                    'payments' => $result['payments'] ?? [],
                    'total_amount' => $result['total_amount'] ?? 0
                ];
            } else {
                // Если создание не удалось, откатываем изменения в кассах
                foreach ($cashboxRevertData as $revertData) {
                    $cashbox = Cashbox::findOne($revertData['cashbox_id']);
                    if ($cashbox) {
                        $cashbox->balance -= $revertData['amount'];
                        $cashbox->save();
                    }
                }
                
                $transaction->rollBack();
                return $result;
            }

        } catch (\Exception $e) {
            $transaction->rollBack();
            return [
                'status' => 'error',
                'message' => 'Ошибка при обновлении выплаты: ' . $e->getMessage()
            ];
        }
    }
    
    /**
     * Валидация данных для обновления выплаты
     */
    private function validateUpdateData(array $data, WorkerPaymentService $mainService): array
    {
        // Используем валидацию из основного сервиса, но с дополнительными проверками
        $errors = [];

        // Проверка обязательных полей для обновления
        if (empty($data['worker_id'])) {
            $errors['worker_id'] = 'Не указан ID работника';
        }

        if (empty($data['month'])) {
            $errors['month'] = 'Не указан месяц';
        }

        // Проверяем, что работник существует
        $worker = Worker::findOne($data['worker_id']);
        if (!$worker) {
            $errors['worker_id'] = 'Работник не найден';
        }

        // Проверяем, что есть существующие выплаты для обновления
        if ($worker) {
            $existingPayments = WorkerFinances::find()
                ->where(['worker_id' => $data['worker_id'], 'month' => $data['month']])
                ->andWhere(['deleted_at' => null])
                ->count();
                
            if ($existingPayments === 0) {
                $errors['payments'] = 'Нет существующих выплат для обновления за указанный месяц';
            }
        }

        if (empty($errors)) {
            // Если базовые проверки прошли, используем полную валидацию из основного сервиса
            return $this->callPrivateMethod($mainService, 'validatePaymentData', [$data]);
        } else {
            return [
                'status' => 'error',
                'errors' => $errors,
                'message' => 'Ошибки валидации при обновлении'
            ];
        }
    }
    
    /**
     * Получить данные о существующих выплатах работника за месяц
     */
    public function getExistingPayments(int $workerId, string $month): array
    {
        try {
            $payments = WorkerFinances::find()
                ->where(['worker_id' => $workerId, 'month' => $month])
                ->andWhere(['deleted_at' => null])
                ->all();
            
            $paymentsByType = [];
            $totalAmount = 0;
            
            foreach ($payments as $payment) {
                $type = $payment->type;
                if (!isset($paymentsByType[$type])) {
                    $paymentsByType[$type] = [];
                }
                $paymentsByType[$type][] = [
                    'id' => $payment->id,
                    'amount' => $payment->amount,
                    'description' => $payment->description,
                    'created_at' => $payment->created_at
                ];
                $totalAmount += $payment->amount;
            }
            
            return [
                'status' => 'success',
                'payments_by_type' => $paymentsByType,
                'total_amount' => $totalAmount,
                'count' => count($payments)
            ];
            
        } catch (\Exception $e) {
            return [
                'status' => 'error',
                'message' => 'Ошибка при получении данных о выплатах: ' . $e->getMessage()
            ];
        }
    }
    
    /**
     * Удалить выплаты работника за месяц
     */
    public function deletePayments(int $workerId, string $month): array
    {
        $transaction = Yii::$app->db->beginTransaction();
        
        try {
            $payments = WorkerFinances::find()
                ->where(['worker_id' => $workerId, 'month' => $month])
                ->andWhere(['deleted_at' => null])
                ->all();
            
            if (empty($payments)) {
                return [
                    'status' => 'error',
                    'message' => 'Нет выплат для удаления'
                ];
            }
            
            $deletedCount = 0;
            $totalAmount = 0;
            
            foreach ($payments as $payment) {
                // Помечаем выплату как удаленную
                $payment->deleted_at = date('Y-m-d H:i:s');
                $payment->save();
                
                // Возвращаем деньги в кассы
                $cashboxDetails = CashboxDetail::find()
                    ->where(['worker_finance_id' => $payment->id])
                    ->andWhere(['deleted_at' => null])
                    ->all();
                
                foreach ($cashboxDetails as $detail) {
                    $cashbox = Cashbox::findOne($detail->cashbox_id);
                    if ($cashbox) {
                        $cashbox->balance += $detail->amount;
                        $cashbox->save();
                    }
                    
                    $detail->deleted_at = date('Y-m-d H:i:s');
                    $detail->save();
                }
                
                $deletedCount++;
                $totalAmount += $payment->amount;
            }
            
            $transaction->commit();
            
            return [
                'status' => 'success',
                'message' => "Удалено выплат: {$deletedCount}",
                'deleted_count' => $deletedCount,
                'total_amount' => $totalAmount
            ];
            
        } catch (\Exception $e) {
            $transaction->rollBack();
            return [
                'status' => 'error',
                'message' => 'Ошибка при удалении выплат: ' . $e->getMessage()
            ];
        }
    }
    
    /**
     * Вспомогательный метод для вызова приватных методов из других классов
     */
    private function callPrivateMethod($object, $methodName, $parameters = [])
    {
        $reflection = new \ReflectionClass(get_class($object));
        $method = $reflection->getMethod($methodName);
        $method->setAccessible(true);
        return $method->invokeArgs($object, $parameters);
    }
}
