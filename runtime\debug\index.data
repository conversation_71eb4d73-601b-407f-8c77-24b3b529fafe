a:55:{s:13:"6888e5db0820b";a:13:{s:3:"tag";s:13:"6888e5db0820b";s:3:"url";s:42:"http://silver/backend/worker-payment/index";s:4:"ajax";i:0;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1753802202.812126;s:10:"statusCode";i:200;s:8:"sqlCount";i:95;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:11500464;s:14:"processingTime";d:0.3869750499725342;}s:13:"6888e5e0397b6";a:13:{s:3:"tag";s:13:"6888e5e0397b6";s:3:"url";s:69:"http://silver/backend/worker-payment/edit?worker_id=107&month=2025-07";s:4:"ajax";i:1;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1753802207.996991;s:10:"statusCode";i:200;s:8:"sqlCount";i:24;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:10514144;s:14:"processingTime";d:0.3290579319000244;}s:13:"6888e6cb382ca";a:13:{s:3:"tag";s:13:"6888e6cb382ca";s:3:"url";s:42:"http://silver/backend/worker-payment/index";s:4:"ajax";i:0;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1753802442.997028;s:10:"statusCode";i:200;s:8:"sqlCount";i:95;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:11500464;s:14:"processingTime";d:0.37017083168029785;}s:13:"6888e8467e592";a:13:{s:3:"tag";s:13:"6888e8467e592";s:3:"url";s:42:"http://silver/backend/worker-payment/index";s:4:"ajax";i:0;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1753802820.958443;s:10:"statusCode";i:200;s:8:"sqlCount";i:93;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:11473440;s:14:"processingTime";d:1.5435888767242432;}s:13:"6888e908956fb";a:13:{s:3:"tag";s:13:"6888e908956fb";s:3:"url";s:42:"http://silver/backend/worker-payment/index";s:4:"ajax";i:0;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1753803016.249295;s:10:"statusCode";i:200;s:8:"sqlCount";i:93;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:11474064;s:14:"processingTime";d:0.43494105339050293;}s:13:"6888e909d918a";a:13:{s:3:"tag";s:13:"6888e909d918a";s:3:"url";s:43:"http://silver/backend/worker-payment/create";s:4:"ajax";i:1;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1753803017.778684;s:10:"statusCode";i:200;s:8:"sqlCount";i:9;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:10209464;s:14:"processingTime";d:0.17963910102844238;}s:13:"6888e90be32da";a:13:{s:3:"tag";s:13:"6888e90be32da";s:3:"url";s:80:"http://silver/backend/worker-payment/get-worker-info?worker_id=107&month=2025-07";s:4:"ajax";i:1;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1753803019.811845;s:10:"statusCode";i:200;s:8:"sqlCount";i:14;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:9288344;s:14:"processingTime";d:0.1469099521636963;}s:13:"6888e918785ad";a:13:{s:3:"tag";s:13:"6888e918785ad";s:3:"url";s:42:"http://silver/backend/worker-payment/store";s:4:"ajax";i:1;s:6:"method";s:4:"POST";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1753803032.290903;s:10:"statusCode";i:200;s:8:"sqlCount";i:64;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:10237616;s:14:"processingTime";d:0.3766770362854004;}s:13:"6888e918d7402";a:13:{s:3:"tag";s:13:"6888e918d7402";s:3:"url";s:76:"http://silver/backend/worker-payment/index?_pjax=%23worker-payment-grid-pjax";s:4:"ajax";i:1;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1753803032.69895;s:10:"statusCode";i:200;s:8:"sqlCount";i:95;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:10964328;s:14:"processingTime";d:0.2855257987976074;}s:13:"6888e91954fff";a:13:{s:3:"tag";s:13:"6888e91954fff";s:3:"url";s:76:"http://silver/backend/worker-payment/index?_pjax=%23worker-payment-grid-pjax";s:4:"ajax";i:1;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1753803033.20583;s:10:"statusCode";i:200;s:8:"sqlCount";i:95;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:10964328;s:14:"processingTime";d:0.22930097579956055;}s:13:"6888e91987bb8";a:13:{s:3:"tag";s:13:"6888e91987bb8";s:3:"url";s:76:"http://silver/backend/worker-payment/index?_pjax=%23worker-payment-grid-pjax";s:4:"ajax";i:1;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1753803033.215541;s:10:"statusCode";i:200;s:8:"sqlCount";i:95;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:10964328;s:14:"processingTime";d:0.41726016998291016;}s:13:"6888e91b7690b";a:13:{s:3:"tag";s:13:"6888e91b7690b";s:3:"url";s:42:"http://silver/backend/worker-payment/index";s:4:"ajax";i:0;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1753803035.365924;s:10:"statusCode";i:200;s:8:"sqlCount";i:95;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:11494864;s:14:"processingTime";d:0.20780110359191895;}s:13:"6888e9621128d";a:13:{s:3:"tag";s:13:"6888e9621128d";s:3:"url";s:42:"http://silver/backend/worker-payment/index";s:4:"ajax";i:0;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1753803105.974157;s:10:"statusCode";i:200;s:8:"sqlCount";i:95;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:11494864;s:14:"processingTime";d:0.17931103706359863;}s:13:"6888e966ea6a6";a:13:{s:3:"tag";s:13:"6888e966ea6a6";s:3:"url";s:69:"http://silver/backend/worker-payment/edit?worker_id=107&month=2025-07";s:4:"ajax";i:1;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1753803110.838358;s:10:"statusCode";i:200;s:8:"sqlCount";i:23;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:10504728;s:14:"processingTime";d:0.1953887939453125;}s:13:"6888e96e5ee8c";a:13:{s:3:"tag";s:13:"6888e96e5ee8c";s:3:"url";s:43:"http://silver/backend/worker-payment/update";s:4:"ajax";i:1;s:6:"method";s:4:"POST";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1753803118.284457;s:10:"statusCode";i:200;s:8:"sqlCount";i:107;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:10619096;s:14:"processingTime";d:0.2639760971069336;}s:13:"6888e96eb02c4";a:13:{s:3:"tag";s:13:"6888e96eb02c4";s:3:"url";s:76:"http://silver/backend/worker-payment/index?_pjax=%23worker-payment-grid-pjax";s:4:"ajax";i:1;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1753803118.603032;s:10:"statusCode";i:200;s:8:"sqlCount";i:95;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:10964328;s:14:"processingTime";d:0.17790603637695312;}s:13:"6888e96ed8cdb";a:13:{s:3:"tag";s:13:"6888e96ed8cdb";s:3:"url";s:76:"http://silver/backend/worker-payment/index?_pjax=%23worker-payment-grid-pjax";s:4:"ajax";i:1;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1753803118.58528;s:10:"statusCode";i:200;s:8:"sqlCount";i:95;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:10964328;s:14:"processingTime";d:0.3754689693450928;}s:13:"6888e9729cbd3";a:13:{s:3:"tag";s:13:"6888e9729cbd3";s:3:"url";s:42:"http://silver/backend/worker-payment/index";s:4:"ajax";i:0;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1753803122.54414;s:10:"statusCode";i:200;s:8:"sqlCount";i:95;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:11494864;s:14:"processingTime";d:0.20125198364257812;}s:13:"6888e97564045";a:13:{s:3:"tag";s:13:"6888e97564045";s:3:"url";s:69:"http://silver/backend/worker-payment/edit?worker_id=107&month=2025-07";s:4:"ajax";i:1;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1753803125.290973;s:10:"statusCode";i:200;s:8:"sqlCount";i:23;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:10504728;s:14:"processingTime";d:0.20305800437927246;}s:13:"6888e980d9ceb";a:13:{s:3:"tag";s:13:"6888e980d9ceb";s:3:"url";s:43:"http://silver/backend/worker-payment/update";s:4:"ajax";i:1;s:6:"method";s:4:"POST";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1753803136.759523;s:10:"statusCode";i:200;s:8:"sqlCount";i:127;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:10819736;s:14:"processingTime";d:0.29993295669555664;}s:13:"6888e98159335";a:13:{s:3:"tag";s:13:"6888e98159335";s:3:"url";s:76:"http://silver/backend/worker-payment/index?_pjax=%23worker-payment-grid-pjax";s:4:"ajax";i:1;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1753803137.107083;s:10:"statusCode";i:200;s:8:"sqlCount";i:95;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:10965120;s:14:"processingTime";d:0.3082709312438965;}s:13:"6888e9817f09c";a:13:{s:3:"tag";s:13:"6888e9817f09c";s:3:"url";s:76:"http://silver/backend/worker-payment/index?_pjax=%23worker-payment-grid-pjax";s:4:"ajax";i:1;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1753803137.093725;s:10:"statusCode";i:200;s:8:"sqlCount";i:95;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:10965120;s:14:"processingTime";d:0.5066869258880615;}s:13:"6888e9862bd56";a:13:{s:3:"tag";s:13:"6888e9862bd56";s:3:"url";s:42:"http://silver/backend/worker-payment/index";s:4:"ajax";i:0;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1753803142.073188;s:10:"statusCode";i:200;s:8:"sqlCount";i:95;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:11495656;s:14:"processingTime";d:0.20813393592834473;}s:13:"6888e98c3a733";a:13:{s:3:"tag";s:13:"6888e98c3a733";s:3:"url";s:69:"http://silver/backend/worker-payment/edit?worker_id=107&month=2025-07";s:4:"ajax";i:1;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1753803148.060571;s:10:"statusCode";i:200;s:8:"sqlCount";i:24;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:10513928;s:14:"processingTime";d:0.23297905921936035;}s:13:"6888e99537949";a:13:{s:3:"tag";s:13:"6888e99537949";s:3:"url";s:43:"http://silver/backend/worker-payment/update";s:4:"ajax";i:1;s:6:"method";s:4:"POST";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1753803157.090507;s:10:"statusCode";i:200;s:8:"sqlCount";i:101;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:10483448;s:14:"processingTime";d:0.2572019100189209;}s:13:"6888ec966a0c0";a:13:{s:3:"tag";s:13:"6888ec966a0c0";s:3:"url";s:42:"http://silver/backend/worker-payment/index";s:4:"ajax";i:0;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1753803926.085634;s:10:"statusCode";i:200;s:8:"sqlCount";i:95;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:11496256;s:14:"processingTime";d:0.473646879196167;}s:13:"6888ec9be35eb";a:13:{s:3:"tag";s:13:"6888ec9be35eb";s:3:"url";s:43:"http://silver/backend/worker-payment/create";s:4:"ajax";i:1;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1753803931.804389;s:10:"statusCode";i:200;s:8:"sqlCount";i:9;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:10209464;s:14:"processingTime";d:0.1791400909423828;}s:13:"6888ec9f35482";a:13:{s:3:"tag";s:13:"6888ec9f35482";s:3:"url";s:69:"http://silver/backend/worker-payment/edit?worker_id=107&month=2025-07";s:4:"ajax";i:1;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1753803935.112284;s:10:"statusCode";i:200;s:8:"sqlCount";i:24;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:10512704;s:14:"processingTime";d:0.23933696746826172;}s:13:"6888ecd84de92";a:13:{s:3:"tag";s:13:"6888ecd84de92";s:3:"url";s:43:"http://silver/backend/worker-payment/update";s:4:"ajax";i:1;s:6:"method";s:4:"POST";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1753803992.165976;s:10:"statusCode";i:200;s:8:"sqlCount";i:101;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:10482224;s:14:"processingTime";d:0.29740309715270996;}s:13:"6888ece14f0dd";a:13:{s:3:"tag";s:13:"6888ece14f0dd";s:3:"url";s:43:"http://silver/backend/worker-payment/update";s:4:"ajax";i:1;s:6:"method";s:4:"POST";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1753804001.19011;s:10:"statusCode";i:200;s:8:"sqlCount";i:101;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:10483448;s:14:"processingTime";d:0.25343990325927734;}s:13:"6888ed6cccdfa";a:13:{s:3:"tag";s:13:"6888ed6cccdfa";s:3:"url";s:42:"http://silver/backend/worker-payment/index";s:4:"ajax";i:0;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1753804140.612246;s:10:"statusCode";i:200;s:8:"sqlCount";i:95;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:11496256;s:14:"processingTime";d:0.31423401832580566;}s:13:"6888ed70e06a1";a:13:{s:3:"tag";s:13:"6888ed70e06a1";s:3:"url";s:43:"http://silver/backend/worker-payment/create";s:4:"ajax";i:1;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1753804144.806251;s:10:"statusCode";i:200;s:8:"sqlCount";i:9;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:10209464;s:14:"processingTime";d:0.17051005363464355;}s:13:"6888ed7461a54";a:13:{s:3:"tag";s:13:"6888ed7461a54";s:3:"url";s:80:"http://silver/backend/worker-payment/get-worker-info?worker_id=108&month=2025-07";s:4:"ajax";i:1;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1753804148.280266;s:10:"statusCode";i:200;s:8:"sqlCount";i:14;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:9288336;s:14:"processingTime";d:0.18109583854675293;}s:13:"6888ed7d5a26f";a:13:{s:3:"tag";s:13:"6888ed7d5a26f";s:3:"url";s:42:"http://silver/backend/worker-payment/store";s:4:"ajax";i:1;s:6:"method";s:4:"POST";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1753804157.27973;s:10:"statusCode";i:200;s:8:"sqlCount";i:48;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:10120224;s:14:"processingTime";d:0.1671609878540039;}s:13:"6888ed7da35ee";a:13:{s:3:"tag";s:13:"6888ed7da35ee";s:3:"url";s:76:"http://silver/backend/worker-payment/index?_pjax=%23worker-payment-grid-pjax";s:4:"ajax";i:1;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1753804157.472524;s:10:"statusCode";i:200;s:8:"sqlCount";i:95;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:10965952;s:14:"processingTime";d:0.27058911323547363;}s:13:"6888ed7e18835";a:13:{s:3:"tag";s:13:"6888ed7e18835";s:3:"url";s:76:"http://silver/backend/worker-payment/index?_pjax=%23worker-payment-grid-pjax";s:4:"ajax";i:1;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1753804157.979291;s:10:"statusCode";i:200;s:8:"sqlCount";i:95;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:10965952;s:14:"processingTime";d:0.20152688026428223;}s:13:"6888ed7e47b54";a:13:{s:3:"tag";s:13:"6888ed7e47b54";s:3:"url";s:76:"http://silver/backend/worker-payment/index?_pjax=%23worker-payment-grid-pjax";s:4:"ajax";i:1;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1753804157.990458;s:10:"statusCode";i:200;s:8:"sqlCount";i:95;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:10965952;s:14:"processingTime";d:0.38450098037719727;}s:13:"6888ed81b4152";a:13:{s:3:"tag";s:13:"6888ed81b4152";s:3:"url";s:69:"http://silver/backend/worker-payment/edit?worker_id=108&month=2025-07";s:4:"ajax";i:1;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1753804161.630315;s:10:"statusCode";i:200;s:8:"sqlCount";i:20;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:10418232;s:14:"processingTime";d:0.18687701225280762;}s:13:"6888ed8c2035d";a:13:{s:3:"tag";s:13:"6888ed8c2035d";s:3:"url";s:43:"http://silver/backend/worker-payment/update";s:4:"ajax";i:1;s:6:"method";s:4:"POST";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1753804172.00824;s:10:"statusCode";i:200;s:8:"sqlCount";i:94;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:10564200;s:14:"processingTime";d:0.27974820137023926;}s:13:"6888ed8c9eb83";a:13:{s:3:"tag";s:13:"6888ed8c9eb83";s:3:"url";s:76:"http://silver/backend/worker-payment/index?_pjax=%23worker-payment-grid-pjax";s:4:"ajax";i:1;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1753804172.342724;s:10:"statusCode";i:200;s:8:"sqlCount";i:95;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:10966744;s:14:"processingTime";d:0.36260008811950684;}s:13:"6888ed8ccb301";a:13:{s:3:"tag";s:13:"6888ed8ccb301";s:3:"url";s:76:"http://silver/backend/worker-payment/index?_pjax=%23worker-payment-grid-pjax";s:4:"ajax";i:1;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1753804172.379154;s:10:"statusCode";i:200;s:8:"sqlCount";i:95;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:10966744;s:14:"processingTime";d:0.5363168716430664;}s:13:"6888ed8d0750c";a:13:{s:3:"tag";s:13:"6888ed8d0750c";s:3:"url";s:76:"http://silver/backend/worker-payment/index?_pjax=%23worker-payment-grid-pjax";s:4:"ajax";i:1;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1753804172.3862;s:10:"statusCode";i:200;s:8:"sqlCount";i:95;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:10966744;s:14:"processingTime";d:0.7290968894958496;}s:13:"6888ed902acfd";a:13:{s:3:"tag";s:13:"6888ed902acfd";s:3:"url";s:69:"http://silver/backend/worker-payment/edit?worker_id=108&month=2025-07";s:4:"ajax";i:1;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1753804176.048896;s:10:"statusCode";i:200;s:8:"sqlCount";i:23;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:10504720;s:14:"processingTime";d:0.21454095840454102;}s:13:"6888ed97efd79";a:13:{s:3:"tag";s:13:"6888ed97efd79";s:3:"url";s:43:"http://silver/backend/worker-payment/update";s:4:"ajax";i:1;s:6:"method";s:4:"POST";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1753804183.88263;s:10:"statusCode";i:200;s:8:"sqlCount";i:81;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:10355864;s:14:"processingTime";d:0.21157503128051758;}s:13:"6888edd7b0ef8";a:13:{s:3:"tag";s:13:"6888edd7b0ef8";s:3:"url";s:43:"http://silver/backend/worker-payment/update";s:4:"ajax";i:1;s:6:"method";s:4:"POST";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1753804247.58354;s:10:"statusCode";i:200;s:8:"sqlCount";i:81;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:10357088;s:14:"processingTime";d:0.2347559928894043;}s:13:"6888ee6027413";a:13:{s:3:"tag";s:13:"6888ee6027413";s:3:"url";s:34:"http://silver/backend/worker/index";s:4:"ajax";i:0;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1753804383.978498;s:10:"statusCode";i:200;s:8:"sqlCount";i:6;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:10933680;s:14:"processingTime";d:0.2593247890472412;}s:13:"6888ef1e636dc";a:13:{s:3:"tag";s:13:"6888ef1e636dc";s:3:"url";s:42:"http://silver/backend/archive-worker/index";s:4:"ajax";i:0;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1753804574.264232;s:10:"statusCode";i:200;s:8:"sqlCount";i:6;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:9920312;s:14:"processingTime";d:0.2255239486694336;}s:13:"6888ef20d9f85";a:13:{s:3:"tag";s:13:"6888ef20d9f85";s:3:"url";s:42:"http://silver/backend/worker-payment/index";s:4:"ajax";i:0;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1753804576.795717;s:10:"statusCode";i:200;s:8:"sqlCount";i:95;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:11500752;s:14:"processingTime";d:0.1932508945465088;}s:13:"6888ef4af38bb";a:13:{s:3:"tag";s:13:"6888ef4af38bb";s:3:"url";s:69:"http://silver/backend/worker-payment/edit?worker_id=108&month=2025-07";s:4:"ajax";i:1;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1753804618.772582;s:10:"statusCode";i:200;s:8:"sqlCount";i:23;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:10504720;s:14:"processingTime";d:0.2658050060272217;}s:13:"6888ef604ea0a";a:13:{s:3:"tag";s:13:"6888ef604ea0a";s:3:"url";s:69:"http://silver/backend/worker-payment/edit?worker_id=108&month=2025-07";s:4:"ajax";i:1;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1753804640.12266;s:10:"statusCode";i:200;s:8:"sqlCount";i:23;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:10504720;s:14:"processingTime";d:0.2566850185394287;}s:13:"6888f05a34e36";a:13:{s:3:"tag";s:13:"6888f05a34e36";s:3:"url";s:69:"http://silver/backend/worker-payment/edit?worker_id=108&month=2025-07";s:4:"ajax";i:1;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1753804889.7536;s:10:"statusCode";i:200;s:8:"sqlCount";i:23;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:10504720;s:14:"processingTime";d:0.46587491035461426;}s:13:"6888f0895d0e7";a:13:{s:3:"tag";s:13:"6888f0895d0e7";s:3:"url";s:43:"http://silver/backend/worker-payment/update";s:4:"ajax";i:1;s:6:"method";s:4:"POST";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1753804937.270454;s:10:"statusCode";i:200;s:8:"sqlCount";i:81;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:10357088;s:14:"processingTime";d:0.2531430721282959;}s:13:"6888f239a7da2";a:13:{s:3:"tag";s:13:"6888f239a7da2";s:3:"url";s:42:"http://silver/backend/worker-payment/index";s:4:"ajax";i:0;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1753805369.405878;s:10:"statusCode";i:200;s:8:"sqlCount";i:95;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:11505192;s:14:"processingTime";d:0.399716854095459;}s:13:"6888f23d49687";a:13:{s:3:"tag";s:13:"6888f23d49687";s:3:"url";s:69:"http://silver/backend/worker-payment/edit?worker_id=108&month=2025-07";s:4:"ajax";i:1;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1753805373.186065;s:10:"statusCode";i:200;s:8:"sqlCount";i:23;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:10509584;s:14:"processingTime";d:0.17670798301696777;}s:13:"6888f24710e45";a:13:{s:3:"tag";s:13:"6888f24710e45";s:3:"url";s:43:"http://silver/backend/worker-payment/update";s:4:"ajax";i:1;s:6:"method";s:4:"POST";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1753805382.94858;s:10:"statusCode";i:200;s:8:"sqlCount";i:81;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:10438592;s:14:"processingTime";d:0.24302077293395996;}}