<?php

$params = require __DIR__ . '/params.php';
$db = require __DIR__ . '/db.php';
$rules = require __DIR__ . '/rules.php';
require __DIR__ . '/helpers.php';

$config = [
    'id' => 'basic',
    'basePath' => dirname(__DIR__),
    'timeZone' => 'Asia/Tashkent',
    'bootstrap' => ['log', 'sessionTimeout', function() {
        \Sentry\init(['dsn' => 'https://<EMAIL>/4508663317987408']);
    }],
    'language' => 'uz',
    'aliases' => [
        '@bower' => '@vendor/bower-asset',
        '@npm'   => '@vendor/npm-asset',
        '@storage' => '@app/storage',
        '@storageUrl' => $params['baseUrl'].'/storage',
        '@storagePath' => '/storage',
    ],
    'components' => [
        'request' => [
            'cookieValidationKey' => 'j2wIdXY2c4OTeiknFMo5zAIEarc9se0j',
            'baseUrl' => '',
            'parsers' => [
                'application/json' => [
                    'class' => \yii\web\JsonParser::class,
                    'asArray' => true,
                ],
            ]
        ],
        'i18n' => [
            'translations' => [
                'app*' => [
                    'class' => 'yii\i18n\PhpMessageSource',
                    'basePath' => '@app/common/languages',
                    'fileMap' => [
                        'app' => 'app.php',
                        'app2' => 'app2.php',
                        'app/error' => 'error.php',
                    ],
                ],
            ],
        ],
        'cache' => [
            'class' => 'yii\caching\FileCache',
        ],
        'session' => [
            'class' => 'yii\web\Session',
            'timeout' => 1800,
            'cookieParams' => ['httponly' => true],
            'useCookies' => true,
        ],
        'user' => [
            'identityClass' => 'app\modules\backend\models\Users',
            'enableAutoLogin' => true,
            'loginUrl' => ['site/login'],
            'authTimeout' => 7200,

        ],
        'errorHandler' => [
            'errorAction' => 'site/error',
        ],
        'mailer' => [
            'class' => \yii\symfonymailer\Mailer::class,
            'viewPath' => '@app/mail',
            // send all mails to a file by default.
            'useFileTransport' => true,
        ],    
            'log' => [
            'traceLevel' => YII_DEBUG ? 3 : 0,
            'targets' => [
                [
                    'class' => 'yii\log\FileTarget',
                    'levels' => ['error', 'warning'],
                ],
                [
                    'class' => 'yii\log\FileTarget',
                    'levels' => ['info'],
                    'categories' => ['repackaging'],
                    'logFile' => '@runtime/logs/repackaging.log',
                    'logVars' => [],
                    'prefix' => function ($message) {
                        return '';
                    },
                ],
                [
                    'class' => 'yii\log\FileTarget',
                    'levels' => ['info', 'error'],
                    'categories' => ['app\modules\backend\controllers\ExcelWorkerController::actionIndex'],
                    'logFile' => '@runtime/logs/excel-import.log',
                    'logVars' => [],
                ],
                [
                    'class' => 'yii\log\FileTarget',
                    'levels' => ['info', 'error'],
                    'categories' => ['excel-client'],
                    'logFile' => '@runtime/logs/excel-client.log',
                    'logVars' => [],
                ],
                [
                    'class' => 'yii\log\FileTarget',
                    'levels' => ['info', 'error'],
                    'categories' => ['worker-payment'],
                    'logFile' => '@runtime/logs/worker-payment.log',
                    'logVars' => [],
                ],
            ],
        ],
        'db' => $db,
        'urlManager' => [
            'enablePrettyUrl' => true,
            'showScriptName' => false,
            'rules' => $rules
        ],
        'authManager' => [
            'class' => 'yii\rbac\DbManager',
        ],
        'queue' => [
            'class' => \yii\queue\file\Queue::class,
            'path' => '@runtime/queue',
            'as log' => \yii\queue\LogBehavior::class,
        ],
        'actionLogger' => [
            'class' => 'app\components\ActionLogger',
        ],
        'sessionTimeout' => [
            'class' => 'app\components\SessionTimeoutComponent',
        ]
        // 'firebase' => [
        //     'class' => 'app\components\FirebaseService',
        //     'apiKey' => $params['firebase']['apiKey'],
        //     'projectId' => $params['firebase']['projectId'],
        //     'credentialsFile' => $params['firebase']['credentialsFile'],
        // ],
    ],
    'modules' => [
        'api' => [
            'class' => 'app\modules\api\ApiModule'
        ],
        'backend' => [
            'basePath' => '@app/modules/backend',
            'class' => 'app\modules\backend\BackendModule',
        ],
        'test' => [
            'class' => 'app\modules\test\TestModule',
        ],
    ],
    'as beforeRequest' => [
        'class' => 'app\components\LanguageBehavior',
    ],
    'params' => $params,
];

if (YII_ENV_DEV) {
    // configuration adjustments for 'dev' environment
    $config['bootstrap'][] = 'debug';
    $config['modules']['debug'] = [
        'class' => 'yii\debug\Module',
        // uncomment the following to add your IP if you are not connecting from localhost.
        //'allowedIPs' => ['127.0.0.1', '::1'],
    ];

    $config['bootstrap'][] = 'gii';
    $config['modules']['gii'] = [
        'class' => 'yii\gii\Module',
        // uncomment the following to add your IP if you are not connecting from localhost.
        'allowedIPs' => ['127.0.0.1'],
    ];
}

return $config;
