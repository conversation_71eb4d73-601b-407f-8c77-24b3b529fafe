<?php
use yii\helpers\Html;
use yii\helpers\ArrayHelper;
use app\common\models\Product;

/* @var $reportData array */
/* @var $startDate string */
/* @var $endDate string */
/* @var $productId int|null */
/* @var $products array */

$this->title = Yii::t('app', 'product_sales_report');
$this->params['breadcrumbs'][] = $this->title;

// Функция форматирования числовых значений: если дробная часть равна 0 — без десятых, иначе с двумя знаками
$formatMoney = function($value) {
    return (fmod($value, 1.0) == 0.0)
        ? Yii::$app->formatter->asDecimal($value, 0)
        : Yii::$app->formatter->asDecimal($value, 2);
};
?>

<style>
    /* Стили для кастомного datetime picker */
    .datetime-container {
        position: relative;
    }

    .custom-datetime-picker {
        position: absolute;
        top: 100%;
        left: 0;
        right: 0;
        background: white;
        border: 1px solid #ddd;
        border-radius: 4px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        z-index: 9999;
        display: none;
        padding: 15px;
        min-width: 300px;
    }

    .custom-datetime-picker.show {
        display: block;
    }

    .datetime-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 15px;
        padding-bottom: 10px;
        border-bottom: 1px solid #eee;
    }

    .datetime-nav-btn {
        background: #f8f9fa;
        border: 1px solid #ddd;
        border-radius: 3px;
        padding: 5px 10px;
        cursor: pointer;
        font-size: 14px;
    }

    .datetime-nav-btn:hover {
        background: #e9ecef;
    }

    .datetime-grid {
        display: grid;
        grid-template-columns: repeat(7, 1fr);
        gap: 2px;
        margin-bottom: 15px;
    }

    .datetime-cell {
        padding: 8px;
        text-align: center;
        cursor: pointer;
        border-radius: 3px;
        font-size: 14px;
    }

    .datetime-cell:hover {
        background: #e9ecef;
    }

    .datetime-cell.selected {
        background: #007bff;
        color: white;
    }

    .datetime-cell.today {
        background: #ffc107;
        color: black;
    }

    .datetime-cell.other-month {
        color: #6c757d;
    }

    .time-inputs {
        display: flex;
        gap: 10px;
        align-items: center;
        margin-bottom: 15px;
    }

    .time-inputs input {
        width: 60px;
        padding: 5px;
        border: 1px solid #ddd;
        border-radius: 3px;
        text-align: center;
    }

    .datetime-actions {
        display: flex;
        justify-content: flex-end;
        gap: 10px;
    }

    .datetime-btn {
        padding: 8px 16px;
        border: none;
        border-radius: 4px;
        cursor: pointer;
        font-size: 14px;
    }

    .datetime-btn.primary {
        background: #007bff;
        color: white;
    }

    .datetime-btn.primary:hover {
        background: #0056b3;
    }

    .datetime-btn.secondary {
        background: #6c757d;
        color: white;
    }

    .datetime-btn.secondary:hover {
        background: #545b62;
    }

    .sales-filters {
        display: flex;
        align-items: end;
        gap: 1rem;
        flex-wrap: wrap;
    }

    .sales-filters .form-group {
        flex: 0 0 auto;
    }

    .sales-filters .form-control {
        min-width: 180px;
    }

    .sales-buttons {
        display: flex;
        gap: 0.5rem;
        flex: 0 0 auto;
    }

    @media (max-width: 768px) {
        .sales-filters {
            flex-direction: column;
            align-items: stretch;
            gap: 0.75rem;
        }

        .sales-filters .form-control {
            min-width: 100%;
        }

        .sales-buttons {
            justify-content: stretch;
        }

        .sales-buttons .btn {
            flex: 1;
        }
    }
</style>

<div class="row align-items-center mb-3">
    <div class="col-md-6">
        <h3 class="mb-0"><?= Html::encode($this->title) ?></h3>
    </div>
    <div class="col-md-6 d-flex justify-content-end">
        <div class="sales-filters justify-content-end">
            <div class="form-group datetime-container">
                <input type="text" id="date-from" class="form-control custom-datetime-input" readonly
                    placeholder="Выберите дату и время"
                    value="<?= date('d.m.Y', strtotime($startDate)) . ' 00:00' ?>">
                <div class="custom-datetime-picker" id="picker-date-from">
                    <div class="datetime-header">
                        <button type="button" class="datetime-nav-btn" data-action="prev-month">&lt;</button>
                        <span class="current-month-year"></span>
                        <button type="button" class="datetime-nav-btn" data-action="next-month">&gt;</button>
                    </div>
                    <div class="datetime-grid"></div>
                    <div class="time-inputs">
                        <label>Время:</label>
                        <input type="number" class="hour-input" min="0" max="23" value="00">
                        <span>:</span>
                        <input type="number" class="minute-input" min="0" max="59" value="00">
                    </div>
                    <div class="datetime-actions">
                        <button type="button" class="datetime-btn secondary" data-action="cancel">Отмена</button>
                        <button type="button" class="datetime-btn primary" data-action="ok">ОК</button>
                    </div>
                </div>
            </div>
            <div class="form-group datetime-container">
                <input type="text" id="date-to" class="form-control custom-datetime-input" readonly
                    placeholder="Выберите дату и время"
                    value="<?= date('d.m.Y', strtotime($endDate)) . ' 23:59' ?>">
                <div class="custom-datetime-picker" id="picker-date-to">
                    <div class="datetime-header">
                        <button type="button" class="datetime-nav-btn" data-action="prev-month">&lt;</button>
                        <span class="current-month-year"></span>
                        <button type="button" class="datetime-nav-btn" data-action="next-month">&gt;</button>
                    </div>
                    <div class="datetime-grid"></div>
                    <div class="time-inputs">
                        <label>Время:</label>
                        <input type="number" class="hour-input" min="0" max="23" value="23">
                        <span>:</span>
                        <input type="number" class="minute-input" min="0" max="59" value="59">
                    </div>
                    <div class="datetime-actions">
                        <button type="button" class="datetime-btn secondary" data-action="cancel">Отмена</button>
                        <button type="button" class="datetime-btn primary" data-action="ok">ОК</button>
                    </div>
                </div>
            </div>
            <div class="form-group">
                <?= Html::dropDownList('product_id', $productId, ['' => Yii::t('app', 'all_products')] + $products, ['class' => 'form-control', 'id' => 'product-select']) ?>
            </div>
            <div class="sales-buttons">
                <button class="btn btn-primary" type="button" id="apply-filter">
                    <i class="fas fa-search"></i> <?= Yii::t('app', 'search') ?>
                </button>
            </div>
        </div>
    </div>
</div>

<?php if (empty($reportData['items'])): ?>
    <div class="alert alert-info text-center mt-3">
        <i class="fas fa-info-circle mr-2"></i> <?= Yii::t('app', 'no_data_found') ?>.
    </div>
<?php else: ?>
<div class="card">
    <div class="card-body p-0">
        <div class="table-responsive">
            <table class="table table-bordered table-striped mb-0" style="min-width:100%;table-layout:fixed;">
                <thead class="thead-light text-center">
                <tr>
                    <th><?= Yii::t('app', 'product') ?></th>
                    <th><?= Yii::t('app', 'block') ?></th>
                    <th><?= Yii::t('app', 'quantity') ?></th>
                    <th><?= Yii::t('app', 'amount') ?></th>
                </tr>
                </thead>
                <tbody>
                <?php foreach ($reportData['items'] as $row): ?>
                    <tr>
                        <td><?= Html::encode($row['product_name']) ?></td>
                        <td class="text-right"><?= (int)$row['total_blocks'] ?></td>
                        <td class="text-right"><?= (int)$row['total_quantity'] ?></td>
                        <td class="text-right"><?= $formatMoney($row['total_amount']) ?></td>
                    </tr>
                <?php endforeach; ?>
                </tbody>
                <tfoot class="bg-light font-weight-bold">
                <tr>
                    <td class="text-right"><?= Yii::t('app', 'total') ?></td>
                    <td class="text-right"><?= $reportData['summary']['totalBlocks'] ?></td>
                    <td class="text-right"><?= $reportData['summary']['totalQuantity'] ?></td>
                    <td class="text-right"><?= $formatMoney($reportData['summary']['totalAmount']) ?></td>
                </tr>
                </tfoot>
            </table>
        </div>
    </div>
</div>
<?php endif; ?>

<script>
    // Кастомный DateTime Picker - точная копия из invoice/sales.php
    class CustomDateTimePicker {
        constructor(inputId, pickerId) {
            this.input = document.getElementById(inputId);
            this.picker = document.getElementById(pickerId);
            this.currentDate = new Date();
            this.selectedDate = new Date();

            this.init();
        }

        init() {
            // Парсим текущее значение поля
            if (this.input.value) {
                const parts = this.input.value.split(' ');
                if (parts.length === 2) {
                    const dateParts = parts[0].split('.');
                    const timeParts = parts[1].split(':');
                    if (dateParts.length === 3 && timeParts.length === 2) {
                        this.selectedDate = new Date(dateParts[2], dateParts[1] - 1, dateParts[0], timeParts[0], timeParts[1]);
                        this.currentDate = new Date(this.selectedDate);
                    }
                }
            }

            this.render();
            this.bindEvents();
        }

        bindEvents() {
            // Открытие picker при клике на input
            this.input.addEventListener('click', () => {
                this.show();
            });

            // Закрытие при клике вне picker
            document.addEventListener('click', (e) => {
                if (!this.picker.contains(e.target) && e.target !== this.input) {
                    this.hide();
                }
            });

            // Навигация по месяцам
            this.picker.querySelector('[data-action="prev-month"]').addEventListener('click', () => {
                this.currentDate.setMonth(this.currentDate.getMonth() - 1);
                this.render();
            });

            this.picker.querySelector('[data-action="next-month"]').addEventListener('click', () => {
                this.currentDate.setMonth(this.currentDate.getMonth() + 1);
                this.render();
            });

            // Кнопки действий
            this.picker.querySelector('[data-action="ok"]').addEventListener('click', () => {
                this.applySelection();
            });

            this.picker.querySelector('[data-action="cancel"]').addEventListener('click', () => {
                this.hide();
            });

            // Изменение времени
            this.picker.querySelector('.hour-input').addEventListener('change', (e) => {
                this.selectedDate.setHours(parseInt(e.target.value));
            });

            this.picker.querySelector('.minute-input').addEventListener('change', (e) => {
                this.selectedDate.setMinutes(parseInt(e.target.value));
            });
        }

        render() {
            this.renderHeader();
            this.renderCalendar();
            this.renderTime();
        }

        renderHeader() {
            const monthNames = ['Январь', 'Февраль', 'Март', 'Апрель', 'Май', 'Июнь',
                'Июль', 'Август', 'Сентябрь', 'Октябрь', 'Ноябрь', 'Декабрь'
            ];
            const header = this.picker.querySelector('.current-month-year');
            header.textContent = `${monthNames[this.currentDate.getMonth()]} ${this.currentDate.getFullYear()}`;
        }

        renderCalendar() {
            const grid = this.picker.querySelector('.datetime-grid');
            grid.innerHTML = '';

            // Заголовки дней недели
            const dayNames = ['Пн', 'Вт', 'Ср', 'Чт', 'Пт', 'Сб', 'Вс'];
            dayNames.forEach(day => {
                const cell = document.createElement('div');
                cell.className = 'datetime-cell';
                cell.style.fontWeight = 'bold';
                cell.style.backgroundColor = '#f8f9fa';
                cell.textContent = day;
                grid.appendChild(cell);
            });

            // Дни месяца
            const firstDay = new Date(this.currentDate.getFullYear(), this.currentDate.getMonth(), 1);
            const lastDay = new Date(this.currentDate.getFullYear(), this.currentDate.getMonth() + 1, 0);
            const startDate = new Date(firstDay);
            startDate.setDate(startDate.getDate() - (firstDay.getDay() === 0 ? 6 : firstDay.getDay() - 1));

            for (let i = 0; i < 42; i++) {
                const cellDate = new Date(startDate);
                cellDate.setDate(startDate.getDate() + i);

                const cell = document.createElement('div');
                cell.className = 'datetime-cell';
                cell.textContent = cellDate.getDate();

                if (cellDate.getMonth() !== this.currentDate.getMonth()) {
                    cell.classList.add('other-month');
                }

                if (this.isSameDay(cellDate, new Date())) {
                    cell.classList.add('today');
                }

                if (this.isSameDay(cellDate, this.selectedDate)) {
                    cell.classList.add('selected');
                }

                cell.addEventListener('click', () => {
                    this.selectedDate.setFullYear(cellDate.getFullYear());
                    this.selectedDate.setMonth(cellDate.getMonth());
                    this.selectedDate.setDate(cellDate.getDate());
                    this.render();
                });

                grid.appendChild(cell);
            }
        }

        renderTime() {
            this.picker.querySelector('.hour-input').value = this.selectedDate.getHours().toString().padStart(2, '0');
            this.picker.querySelector('.minute-input').value = this.selectedDate.getMinutes().toString().padStart(2, '0');
        }

        isSameDay(date1, date2) {
            return date1.getDate() === date2.getDate() &&
                date1.getMonth() === date2.getMonth() &&
                date1.getFullYear() === date2.getFullYear();
        }

        show() {
            this.picker.classList.add('show');
        }

        hide() {
            this.picker.classList.remove('show');
        }

        applySelection() {
            const day = this.selectedDate.getDate().toString().padStart(2, '0');
            const month = (this.selectedDate.getMonth() + 1).toString().padStart(2, '0');
            const year = this.selectedDate.getFullYear();
            const hours = this.selectedDate.getHours().toString().padStart(2, '0');
            const minutes = this.selectedDate.getMinutes().toString().padStart(2, '0');

            this.input.value = `${day}.${month}.${year} ${hours}:${minutes}`;
            this.hide();
        }

        getISOString() {
            return this.selectedDate.toISOString().slice(0, 16);
        }
    }

    // Инициализация обработчиков для фильтрации
    (function() {
        // Функция инициализации DataTable (делаем глобальной)
        window.initProductSalesDataTable = function(container) {
            const targetContainer = container || document;
            
            if (typeof jQuery !== 'undefined' && jQuery.fn.DataTable) {
                targetContainer.querySelectorAll('table').forEach(function(table) {
                    const $table = jQuery(table);
                    
                    // Пропускаем таблицы с классом no-datatables
                    if ($table.hasClass('no-datatables')) {
                        return;
                    }
                    
                    // Уничтожаем предыдущий DataTable если есть
                    if (jQuery.fn.DataTable.isDataTable(table)) {
                        $table.DataTable().destroy();
                    }
                    
                    try {
                        $table.DataTable({
                            "pageLength": 25,
                            "responsive": true,
                            "language": {
                                "search": "Поиск:",
                                "lengthMenu": "Показать _MENU_ записей",
                                "zeroRecords": "Ничего не найдено",
                                "info": "Показано _START_ до _END_ из _TOTAL_ записей",
                                "infoEmpty": "Нет данных",
                                "infoFiltered": "(отфильтровано из _MAX_ записей)",
                                "paginate": {
                                    "first": "Первая",
                                    "last": "Последняя",
                                    "next": "Следующая",
                                    "previous": "Предыдущая"
                                }
                            }
                        });
                    } catch (e) {
                        console.warn('DataTables initialization failed for table:', e);
                    }
                });
            }
        };

        // Функция инициализации календарей (делаем глобальной)
        window.initProductSalesDatePickers = function() {
            if (document.getElementById('date-from') && document.getElementById('date-to')) {
                // Инициализация кастомных datetime picker
                const dateFromPicker = new CustomDateTimePicker('date-from', 'picker-date-from');
                const dateToPicker = new CustomDateTimePicker('date-to', 'picker-date-to');

                // Функция для конвертации формата даты
                function convertToISOFormat(dateStr) {
                    if (!dateStr) return '';
                    const parts = dateStr.split(' ');
                    if (parts.length !== 2) return '';

                    const dateParts = parts[0].split('.');
                    const timeParts = parts[1].split(':');

                    if (dateParts.length !== 3 || timeParts.length !== 2) return '';

                    const year = dateParts[2];
                    const month = dateParts[1].padStart(2, '0');
                    const day = dateParts[0].padStart(2, '0');
                    const hour = timeParts[0].padStart(2, '0');
                    const minute = timeParts[1].padStart(2, '0');

                    return `${year}-${month}-${day}T${hour}:${minute}`;
                }

                // Обработчик для кнопки применения фильтра
                const applyBtn = document.getElementById('apply-filter');
                if (applyBtn) {
                    applyBtn.addEventListener('click', function() {
                        const dateFromValue = document.getElementById('date-from').value;
                        const dateToValue = document.getElementById('date-to').value;
                        const productIdValue = document.getElementById('product-select').value;

                        if (!dateFromValue || !dateToValue) {
                            alert('Пожалуйста, выберите обе даты');
                            return;
                        }

                        // Конвертируем в ISO формат для сравнения
                        const dateFromISO = convertToISOFormat(dateFromValue);
                        const dateToISO = convertToISOFormat(dateToValue);

                        if (dateFromISO > dateToISO) {
                            alert('Дата "от" не может быть больше даты "до"');
                            return;
                        }

                        // Создаем URL с параметрами фильтра для AJAX запроса
                        const baseUrl = '/backend/report/product-sales';
                        const params = new URLSearchParams();
                        params.set('start_date', dateFromISO.split('T')[0]); // Только дата без времени
                        params.set('end_date', dateToISO.split('T')[0]);     // Только дата без времени
                        
                        if (productIdValue) {
                            params.set('product_id', productIdValue);
                        }

                        // Отправляем AJAX запрос для обновления содержимого вкладки
                        const xhr = new XMLHttpRequest();
                        xhr.open('GET', baseUrl + '?' + params.toString(), true);
                        
                        xhr.onload = function() {
                            if (xhr.status === 200) {
                                // Находим контейнер текущей вкладки и обновляем его содержимое
                                const tabContent = document.querySelector('#product-sales .report-content');
                                if (tabContent) {
                                    tabContent.innerHTML = xhr.responseText;
                                    
                                    // Переинициализируем календари для нового содержимого
                                    setTimeout(function() {
                                        window.initProductSalesDatePickers();
                                        
                                        // Инициализируем DataTable для новой таблицы
                                        window.initProductSalesDataTable(tabContent);
                                    }, 100);
                                }
                            } else {
                                alert('Ошибка при загрузке отчета');
                            }
                        };
                        
                        xhr.onerror = function() {
                            alert('Ошибка сети при загрузке отчета');
                        };
                        
                        xhr.send();
                    });
                }
            }
        };

        // Инициализируем сразу, если DOM уже готов
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', function() {
                window.initProductSalesDatePickers();
                window.initProductSalesDataTable();
            });
        } else {
            // DOM уже загружен (например, при AJAX загрузке)
            window.initProductSalesDatePickers();
            window.initProductSalesDataTable();
        }
    })();
</script>