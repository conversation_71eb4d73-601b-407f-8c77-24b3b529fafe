<?php

namespace app\modules\backend\services\report;

use yii\db\Query;
use yii\helpers\ArrayHelper;
use app\common\models\Sales;

/**
 * Сервис для формирования отчета по продажам продуктов по периодам
 */
class ProductSalesReportService
{
    /**
     * Получить отчет по продажам продуктов
     * 
     * @param string $period Период группировки (не используется, оставлен для совместимости)
     * @param string $startDate Начальная дата для фильтрации
     * @param string $endDate Конечная дата для фильтрации
     * @param int|null $productId ID продукта для фильтрации
     * @return array Массив с данными отчета
     */
    public function getProductSalesReport($period = 'day', $startDate = null, $endDate = null, $productId = null)
    {
        // Определяем форматы для отображения даты
        $dateLabel = 'Дата';
        
        if (!$startDate) {
            $startDate = date('Y-m-d');
        }
        
        if (!$endDate) {
            $endDate = date('Y-m-d');
        }
        
        // Получаем данные о бонусах по продуктам за весь период
        $bonusQuery = new Query();
        $bonusQuery->select([
                'sb.product_id',
                'SUM(sb.quantity) as bonus_quantity'
            ])
            ->from(['sb' => 'sales_bonus'])
            ->leftJoin(['s' => 'sales'], 'sb.sale_id = s.id')
            ->where(['IS', 'sb.deleted_at', null])
            ->andWhere(['IS', 's.deleted_at', null])
            ->andWhere(['>=', 's.created_at', $startDate . ' 00:00:00'])
            ->andWhere(['<=', 's.created_at', $endDate . ' 23:59:59'])
            ->groupBy(['sb.product_id']);
            
        if ($productId) {
            $bonusQuery->andWhere(['sb.product_id' => $productId]);
        }
        
        $bonusData = [];
        foreach ($bonusQuery->all() as $row) {
            $bonusData[$row['product_id']] = $row['bonus_quantity'];
        }
        
        // Запрос для получения данных о продажах продуктов за весь период
        $query = new Query();
        $query->select([
                'p.id as product_id',
                'p.name as product_name',
                'p.size as size',
                'SUM(sd.quantity) as sales_quantity',
                'SUM(sd.special_price * sd.quantity) as total_amount'
            ])
            ->from(['sd' => 'sales_detail'])
            ->leftJoin(['s' => 'sales'], 'sd.sale_id = s.id')
            ->leftJoin(['p' => 'product'], 'sd.product_id = p.id')
            ->where(['IN', 's.status', [Sales::STATUS_NEW, Sales::STATUS_CONFIRMED]])
            ->andWhere(['IS', 'sd.deleted_at', null])
            ->andWhere(['IS', 's.deleted_at', null]);
        
        // Применяем фильтры по дате - исправляем для работы с временными зонами
        $query->andWhere(['>=', 's.created_at', $startDate . ' 00:00:00']);
        $query->andWhere(['<=', 's.created_at', $endDate . ' 23:59:59']);
        
        if ($productId) {
            $query->andWhere(['sd.product_id' => $productId]);
        }
        
        // Группируем по продукту
        $query->groupBy(['p.id', 'p.name', 'p.size']);
        $query->orderBy(['p.name' => SORT_ASC]);
        
        $salesData = $query->all();
        
        // Объединяем данные о продажах и бонусах
        $result = [];
        foreach ($salesData as $row) {
            $bonusQuantity = isset($bonusData[$row['product_id']]) ? $bonusData[$row['product_id']] : 0;
            $totalQuantity = $row['sales_quantity'] + $bonusQuantity;
            
            $result[] = [
                'product_id' => $row['product_id'],
                'product_name' => $row['product_name'],
                'size' => $row['size'],
                'total_quantity' => $totalQuantity,
                'total_blocks' => ceil($totalQuantity / ($row['size'] ?: 1)),
                'total_amount' => $row['total_amount'],
                'period_label' => $startDate . ' - ' . $endDate
            ];
        }
        
        // Добавляем бонусы для продуктов, которые не были проданы
        foreach ($bonusData as $productId => $bonusQuantity) {
            $found = false;
            
            foreach ($result as $row) {
                if ($row['product_id'] == $productId) {
                    $found = true;
                    break;
                }
            }
            
            if (!$found) {
                // Получаем информацию о продукте
                $product = (new Query())
                    ->select(['id', 'name', 'size'])
                    ->from(['product'])
                    ->where(['id' => $productId])
                    ->one();
                
                if ($product) {
                    $result[] = [
                        'product_id' => $product['id'],
                        'product_name' => $product['name'],
                        'size' => $product['size'],
                        'total_quantity' => $bonusQuantity,
                        'total_blocks' => ceil($bonusQuantity / ($product['size'] ?: 1)),
                        'total_amount' => 0,
                        'period_label' => $startDate . ' - ' . $endDate
                    ];
                }
            }
        }
        
        // Сортируем результат по имени продукта
        usort($result, function($a, $b) {
            return strcmp($a['product_name'], $b['product_name']);
        });
        
        // Подготовка итоговых данных
        $totalQuantity = array_sum(ArrayHelper::getColumn($result, 'total_quantity'));
        $totalBlocks = array_sum(ArrayHelper::getColumn($result, 'total_blocks'));
        $totalAmount = array_sum(ArrayHelper::getColumn($result, 'total_amount'));
        
        return [
            'items' => $result,
            'dateLabel' => $dateLabel,
            'summary' => [
                'totalQuantity' => $totalQuantity,
                'totalBlocks' => $totalBlocks,
                'totalAmount' => $totalAmount
            ]
        ];
    }
}